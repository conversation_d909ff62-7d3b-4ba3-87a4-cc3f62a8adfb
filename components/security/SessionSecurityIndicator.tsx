'use client';

import { useState, useEffect, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu';
import { Shield, ShieldCheck, AlertTriangle, RefreshCw, Clock, LogOut, RotateCcw } from 'lucide-react';
import {
  validateSessionClient,
  refreshSession,
  getSessionSecurityStatus,
  shouldRefreshSession,
  clearAllAuthData,
  type SessionInfo,
  type SessionSecurityStatus
} from '@/utils/session-manager';
import { useRouter } from 'next/navigation';

interface SessionSecurityIndicatorProps {
  className?: string;
  showDetails?: boolean;
  autoRefresh?: boolean;
}

export default function SessionSecurityIndicator({
  className = '',
  showDetails = false,
  autoRefresh = true
}: SessionSecurityIndicatorProps) {
  const [sessionInfo, setSessionInfo] = useState<SessionInfo | null>(null);
  const [securityStatus, setSecurityStatus] = useState<SessionSecurityStatus | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [lastCheck, setLastCheck] = useState<Date | null>(null);
  const [isSigningOut, setIsSigningOut] = useState(false);
  const router = useRouter();

  // Check session status
  const checkSessionStatus = useCallback(async (force: boolean = false) => {
    if (isLoading && !force) return;

    setIsLoading(true);
    try {
      // Use shorter timeout for UI interactions to avoid hanging
      const timeoutMs = force ? 8000 : 12000;
      const session = await validateSessionClient(!force, timeoutMs);
      const status = getSessionSecurityStatus(session);

      setSessionInfo(session);
      setSecurityStatus(status);
      setLastCheck(new Date());

      // Auto-refresh if session is expiring soon
      if (autoRefresh && shouldRefreshSession(session) && !isRefreshing) {
        setIsRefreshing(true);
        try {
          const refreshedSession = await refreshSession();
          const refreshedStatus = getSessionSecurityStatus(refreshedSession);

          setSessionInfo(refreshedSession);
          setSecurityStatus(refreshedStatus);
          setLastCheck(new Date());
        } catch (refreshError) {
          console.error('Error refreshing session:', refreshError);
        } finally {
          setIsRefreshing(false);
        }
      }
    } catch (error) {
      console.error('Error checking session status:', error);
    } finally {
      setIsLoading(false);
    }
  }, [autoRefresh]);

  // Refresh session
  const handleRefreshSession = useCallback(async () => {
    setIsRefreshing(true);
    try {
      // Use shorter timeout for user-initiated refresh
      const refreshedSession = await refreshSession(10000);
      const status = getSessionSecurityStatus(refreshedSession);

      setSessionInfo(refreshedSession);
      setSecurityStatus(status);
      setLastCheck(new Date());
    } catch (error) {
      console.error('Error refreshing session:', error);
    } finally {
      setIsRefreshing(false);
    }
  }, []);

  // Sign out and redirect
  const handleSignOut = useCallback(async () => {
    setIsSigningOut(true);
    try {
      // Clear all authentication data including cache and cookies
      await clearAllAuthData();
      router.push('/sign-in');
    } catch (error) {
      console.error('Error signing out:', error);
    } finally {
      setIsSigningOut(false);
    }
  }, [router]);

  // Force session refresh (clear cache and re-validate)
  const handleForceRefresh = useCallback(async () => {
    await checkSessionStatus(true);
  }, [checkSessionStatus]);

  // Initial check and periodic updates
  useEffect(() => {
    // Initial check with delay to avoid render conflicts
    const initialTimer = setTimeout(() => {
      checkSessionStatus();
    }, 100);

    let interval: NodeJS.Timeout | null = null;
    if (autoRefresh) {
      interval = setInterval(() => {
        checkSessionStatus();
      }, 60000); // Check every minute
    }

    return () => {
      clearTimeout(initialTimer);
      if (interval) clearInterval(interval);
    };
  }, [autoRefresh]); // Only depend on autoRefresh

  // Get appropriate icon based on security level
  const getSecurityIcon = () => {
    if (isLoading || isRefreshing) {
      return <RefreshCw className="h-4 w-4 animate-spin" />;
    }

    if (!securityStatus) {
      return <Shield className="h-4 w-4 text-gray-400" />;
    }

    switch (securityStatus.level) {
      case 'secure':
        return <ShieldCheck className="h-4 w-4 text-green-500" />;
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case 'critical':
        return <Shield className="h-4 w-4 text-red-500" />;
      default:
        return <Shield className="h-4 w-4 text-gray-400" />;
    }
  };

  // Get badge variant based on security level
  const getBadgeVariant = () => {
    if (!securityStatus) return 'secondary';

    switch (securityStatus.level) {
      case 'secure':
        return 'default';
      case 'warning':
        return 'secondary';
      case 'critical':
        return 'destructive';
      default:
        return 'secondary';
    }
  };

  // Get badge color classes
  const getBadgeClasses = () => {
    if (!securityStatus) return 'bg-gray-100 text-gray-600 border-gray-200';

    switch (securityStatus.level) {
      case 'secure':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'warning':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'critical':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-600 border-gray-200';
    }
  };

  if (showDetails) {
    return (
      <div className={`flex items-center space-x-3 ${className}`}>
        <div className="flex items-center space-x-2">
          {getSecurityIcon()}
          <Badge variant={getBadgeVariant()} className={getBadgeClasses()}>
            {securityStatus?.icon} {securityStatus?.message || 'Checking...'}
          </Badge>
        </div>

        <div className="flex items-center space-x-1">
          {/* Actions based on security level */}
          {securityStatus?.level === 'secure' && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleForceRefresh}
              disabled={isLoading}
              className="h-8 px-3"
            >
              <RefreshCw className={`h-3 w-3 mr-1 ${isLoading ? 'animate-spin' : ''}`} />
              Check Status
            </Button>
          )}

          {securityStatus?.level === 'warning' && (
            <>
              <Button
                variant="outline"
                size="sm"
                onClick={handleRefreshSession}
                disabled={isRefreshing}
                className="h-8 px-3 bg-yellow-50 border-yellow-200 text-yellow-700 hover:bg-yellow-100"
              >
                <Clock className={`h-3 w-3 mr-1 ${isRefreshing ? 'animate-spin' : ''}`} />
                Extend Session
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleForceRefresh}
                disabled={isLoading}
                className="h-8 px-3"
              >
                <RotateCcw className={`h-3 w-3 mr-1 ${isLoading ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
            </>
          )}

          {securityStatus?.level === 'critical' && (
            <>
              <Button
                variant="outline"
                size="sm"
                onClick={handleSignOut}
                disabled={isSigningOut}
                className="h-8 px-3 bg-red-50 border-red-200 text-red-700 hover:bg-red-100"
              >
                <LogOut className={`h-3 w-3 mr-1 ${isSigningOut ? 'animate-spin' : ''}`} />
                {isSigningOut ? 'Signing Out...' : 'Sign Out & Sign In'}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleForceRefresh}
                disabled={isLoading}
                className="h-8 px-3"
              >
                <RotateCcw className={`h-3 w-3 mr-1 ${isLoading ? 'animate-spin' : ''}`} />
                Try Refresh
              </Button>
            </>
          )}
        </div>

        {lastCheck && (
          <span className="text-xs text-muted-foreground">
            Last checked: {lastCheck.toLocaleTimeString()}
          </span>
        )}
      </div>
    );
  }

  // Compact version for header
  return (
    <TooltipProvider>
      <DropdownMenu>
        <Tooltip>
          <TooltipTrigger asChild>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className={`h-8 w-8 p-0 ${className}`}
                disabled={isLoading}
              >
                {getSecurityIcon()}
              </Button>
            </DropdownMenuTrigger>
          </TooltipTrigger>
          <TooltipContent>
            <p>{securityStatus?.message || 'Session Status'}</p>
          </TooltipContent>
        </Tooltip>

        <DropdownMenuContent align="end" className="w-64">
          <div className="p-3">
            <div className="flex items-center space-x-2 mb-2">
              {getSecurityIcon()}
              <Badge variant={getBadgeVariant()} className={getBadgeClasses()}>
                {securityStatus?.icon} {securityStatus?.message || 'Checking...'}
              </Badge>
            </div>

            {securityStatus?.details && (
              <div className="space-y-1 mb-3">
                {securityStatus.details.map((detail, index) => (
                  <p key={index} className="text-xs text-muted-foreground">
                    {detail}
                  </p>
                ))}
              </div>
            )}

            {lastCheck && (
              <p className="text-xs text-muted-foreground mb-3">
                Last checked: {lastCheck.toLocaleTimeString()}
              </p>
            )}
          </div>

          <DropdownMenuSeparator />

          {/* Actions based on security level */}
          {securityStatus?.level === 'secure' && (
            <DropdownMenuItem onClick={handleForceRefresh} disabled={isLoading}>
              <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
              Check Status
            </DropdownMenuItem>
          )}

          {securityStatus?.level === 'warning' && (
            <>
              <DropdownMenuItem onClick={handleRefreshSession} disabled={isRefreshing}>
                <Clock className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
                Extend Session
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleForceRefresh} disabled={isLoading}>
                <RotateCcw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                Refresh Status
              </DropdownMenuItem>
            </>
          )}

          {securityStatus?.level === 'critical' && (
            <>
              <DropdownMenuItem
                onClick={handleSignOut}
                disabled={isSigningOut}
                className="text-red-600 focus:text-red-600 focus:bg-red-50"
              >
                <LogOut className={`h-4 w-4 mr-2 ${isSigningOut ? 'animate-spin' : ''}`} />
                {isSigningOut ? 'Signing Out...' : 'Sign Out & Sign In Again'}
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleForceRefresh} disabled={isLoading}>
                <RotateCcw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                Try Refresh First
              </DropdownMenuItem>
            </>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
    </TooltipProvider>
  );
}

/**
 * Simple badge version for inline display
 */
export function SessionSecurityBadge({ className = '' }: { className?: string }) {
  return <SessionSecurityIndicator className={className} showDetails={false} />;
}

/**
 * Detailed version for settings/security pages
 */
export function SessionSecurityDetails({ className = '' }: { className?: string }) {
  return <SessionSecurityIndicator className={className} showDetails={true} />;
}
