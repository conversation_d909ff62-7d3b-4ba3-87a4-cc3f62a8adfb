'use client';

import { useState, useEffect, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu';
import { Shield, ShieldCheck, AlertTriangle, RefreshCw, Clock } from 'lucide-react';
import { 
  validateSessionClient, 
  refreshSession, 
  getSessionSecurityStatus,
  shouldRefreshSession,
  type SessionInfo,
  type SessionSecurityStatus 
} from '@/utils/session-manager';

interface SessionSecurityIndicatorProps {
  className?: string;
  showDetails?: boolean;
  autoRefresh?: boolean;
}

export default function SessionSecurityIndicator({
  className = '',
  showDetails = false,
  autoRefresh = true
}: SessionSecurityIndicatorProps) {
  const [sessionInfo, setSessionInfo] = useState<SessionInfo | null>(null);
  const [securityStatus, setSecurityStatus] = useState<SessionSecurityStatus | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [lastCheck, setLastCheck] = useState<Date | null>(null);

  // Check session status
  const checkSessionStatus = useCallback(async (force: boolean = false) => {
    if (isLoading && !force) return;
    
    setIsLoading(true);
    try {
      const session = await validateSessionClient(!force);
      const status = getSessionSecurityStatus(session);
      
      setSessionInfo(session);
      setSecurityStatus(status);
      setLastCheck(new Date());
      
      // Auto-refresh if session is expiring soon
      if (autoRefresh && shouldRefreshSession(session) && !isRefreshing) {
        await handleRefreshSession();
      }
    } catch (error) {
      console.error('Error checking session status:', error);
    } finally {
      setIsLoading(false);
    }
  }, [isLoading, autoRefresh, isRefreshing]);

  // Refresh session
  const handleRefreshSession = useCallback(async () => {
    setIsRefreshing(true);
    try {
      const refreshedSession = await refreshSession();
      const status = getSessionSecurityStatus(refreshedSession);
      
      setSessionInfo(refreshedSession);
      setSecurityStatus(status);
      setLastCheck(new Date());
    } catch (error) {
      console.error('Error refreshing session:', error);
    } finally {
      setIsRefreshing(false);
    }
  }, []);

  // Initial check and periodic updates
  useEffect(() => {
    checkSessionStatus();
    
    if (autoRefresh) {
      const interval = setInterval(() => {
        checkSessionStatus();
      }, 60000); // Check every minute
      
      return () => clearInterval(interval);
    }
  }, [checkSessionStatus, autoRefresh]);

  // Get appropriate icon based on security level
  const getSecurityIcon = () => {
    if (isLoading || isRefreshing) {
      return <RefreshCw className="h-4 w-4 animate-spin" />;
    }
    
    if (!securityStatus) {
      return <Shield className="h-4 w-4 text-gray-400" />;
    }
    
    switch (securityStatus.level) {
      case 'secure':
        return <ShieldCheck className="h-4 w-4 text-green-500" />;
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case 'critical':
        return <Shield className="h-4 w-4 text-red-500" />;
      default:
        return <Shield className="h-4 w-4 text-gray-400" />;
    }
  };

  // Get badge variant based on security level
  const getBadgeVariant = () => {
    if (!securityStatus) return 'secondary';
    
    switch (securityStatus.level) {
      case 'secure':
        return 'default';
      case 'warning':
        return 'secondary';
      case 'critical':
        return 'destructive';
      default:
        return 'secondary';
    }
  };

  // Get badge color classes
  const getBadgeClasses = () => {
    if (!securityStatus) return 'bg-gray-100 text-gray-600 border-gray-200';
    
    switch (securityStatus.level) {
      case 'secure':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'warning':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'critical':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-600 border-gray-200';
    }
  };

  if (showDetails) {
    return (
      <div className={`flex items-center space-x-3 ${className}`}>
        <div className="flex items-center space-x-2">
          {getSecurityIcon()}
          <Badge variant={getBadgeVariant()} className={getBadgeClasses()}>
            {securityStatus?.icon} {securityStatus?.message || 'Checking...'}
          </Badge>
        </div>
        
        <div className="flex items-center space-x-1">
          <Button
            variant="outline"
            size="sm"
            onClick={() => checkSessionStatus(true)}
            disabled={isLoading}
            className="h-8 px-3"
          >
            <RefreshCw className={`h-3 w-3 mr-1 ${isLoading ? 'animate-spin' : ''}`} />
            Check Status
          </Button>
          
          {sessionInfo && shouldRefreshSession(sessionInfo) && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefreshSession}
              disabled={isRefreshing}
              className="h-8 px-3"
            >
              <Clock className={`h-3 w-3 mr-1 ${isRefreshing ? 'animate-spin' : ''}`} />
              Extend Session
            </Button>
          )}
        </div>
        
        {lastCheck && (
          <span className="text-xs text-muted-foreground">
            Last checked: {lastCheck.toLocaleTimeString()}
          </span>
        )}
      </div>
    );
  }

  // Compact version for header
  return (
    <TooltipProvider>
      <DropdownMenu>
        <Tooltip>
          <TooltipTrigger asChild>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className={`h-8 w-8 p-0 ${className}`}
                disabled={isLoading}
              >
                {getSecurityIcon()}
              </Button>
            </DropdownMenuTrigger>
          </TooltipTrigger>
          <TooltipContent>
            <p>{securityStatus?.message || 'Session Status'}</p>
          </TooltipContent>
        </Tooltip>
        
        <DropdownMenuContent align="end" className="w-64">
          <div className="p-3">
            <div className="flex items-center space-x-2 mb-2">
              {getSecurityIcon()}
              <Badge variant={getBadgeVariant()} className={getBadgeClasses()}>
                {securityStatus?.icon} {securityStatus?.message || 'Checking...'}
              </Badge>
            </div>
            
            {securityStatus?.details && (
              <div className="space-y-1 mb-3">
                {securityStatus.details.map((detail, index) => (
                  <p key={index} className="text-xs text-muted-foreground">
                    {detail}
                  </p>
                ))}
              </div>
            )}
            
            {lastCheck && (
              <p className="text-xs text-muted-foreground mb-3">
                Last checked: {lastCheck.toLocaleTimeString()}
              </p>
            )}
          </div>
          
          <DropdownMenuSeparator />
          
          <DropdownMenuItem onClick={() => checkSessionStatus(true)} disabled={isLoading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Check Status
          </DropdownMenuItem>
          
          {sessionInfo && shouldRefreshSession(sessionInfo) && (
            <DropdownMenuItem onClick={handleRefreshSession} disabled={isRefreshing}>
              <Clock className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
              Extend Session
            </DropdownMenuItem>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
    </TooltipProvider>
  );
}

/**
 * Simple badge version for inline display
 */
export function SessionSecurityBadge({ className = '' }: { className?: string }) {
  return <SessionSecurityIndicator className={className} showDetails={false} />;
}

/**
 * Detailed version for settings/security pages
 */
export function SessionSecurityDetails({ className = '' }: { className?: string }) {
  return <SessionSecurityIndicator className={className} showDetails={true} />;
}
