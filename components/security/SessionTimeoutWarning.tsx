'use client';

import { useState, useEffect, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Clock, AlertTriangle, RefreshCw, X } from 'lucide-react';
import { useSessionStatus } from '@/hooks/useSessionMonitor';
import { refreshSession } from '@/utils/session-manager';

interface SessionTimeoutWarningProps {
  className?: string;
  warningThreshold?: number; // milliseconds
  autoExtend?: boolean;
}

export default function SessionTimeoutWarning({
  className = '',
  warningThreshold = 5 * 60 * 1000, // 5 minutes
  autoExtend = false
}: SessionTimeoutWarningProps) {
  const [isExtending, setIsExtending] = useState(false);
  const [hasAutoExtended, setHasAutoExtended] = useState(false);

  // Use a simpler approach to avoid infinite loops
  const { sessionInfo } = useSessionStatus();
  const [showWarning, setShowWarning] = useState(false);
  const [timeLeft, setTimeLeft] = useState<number | null>(null);

  // Check if we should show warning
  useEffect(() => {
    if (sessionInfo?.isExpiringSoon && sessionInfo.timeUntilExpiry) {
      if (sessionInfo.timeUntilExpiry <= warningThreshold) {
        setShowWarning(true);
        setTimeLeft(sessionInfo.timeUntilExpiry);
      } else {
        setShowWarning(false);
        setTimeLeft(null);
      }
    } else {
      setShowWarning(false);
      setTimeLeft(null);
    }
  }, [sessionInfo?.isExpiringSoon, sessionInfo?.timeUntilExpiry, warningThreshold]);

  const dismissWarning = useCallback(() => {
    setShowWarning(false);
  }, []);

  // Format time remaining
  const formatTimeLeft = (milliseconds: number | null): string => {
    if (!milliseconds) return '0:00';

    const totalSeconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;

    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  // Handle session extension
  const handleExtendSession = useCallback(async () => {
    setIsExtending(true);
    try {
      await refreshSession();
      dismissWarning();
      setHasAutoExtended(false);
    } catch (error) {
      console.error('Failed to extend session:', error);
    } finally {
      setIsExtending(false);
    }
  }, [dismissWarning]);

  // Auto-extend session if enabled and threshold reached
  useEffect(() => {
    if (autoExtend && showWarning && timeLeft && timeLeft <= 60000 && !hasAutoExtended) {
      // Auto-extend when 1 minute or less remaining
      setHasAutoExtended(true);
      handleExtendSession();
    }
  }, [autoExtend, showWarning, timeLeft, hasAutoExtended]);

  // Reset auto-extend flag when warning is dismissed
  useEffect(() => {
    if (!showWarning) {
      setHasAutoExtended(false);
    }
  }, [showWarning]);

  if (!showWarning || !timeLeft) {
    return null;
  }

  const isUrgent = timeLeft <= 60000; // Less than 1 minute
  const isCritical = timeLeft <= 30000; // Less than 30 seconds

  return (
    <div className={`fixed top-4 right-4 z-50 ${className}`}>
      <Card className={`w-80 shadow-lg border-2 ${
        isCritical ? 'border-red-500 bg-red-50' :
        isUrgent ? 'border-orange-500 bg-orange-50' :
        'border-yellow-500 bg-yellow-50'
      }`}>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center text-sm">
              <AlertTriangle className={`h-4 w-4 mr-2 ${
                isCritical ? 'text-red-600' :
                isUrgent ? 'text-orange-600' :
                'text-yellow-600'
              }`} />
              Session Expiring Soon
            </CardTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={dismissWarning}
              className="h-6 w-6 p-0"
            >
              <X className="h-3 w-3" />
            </Button>
          </div>
        </CardHeader>

        <CardContent className="pt-0">
          <div className="space-y-3">
            <Alert className={`border ${
              isCritical ? 'border-red-200 bg-red-50' :
              isUrgent ? 'border-orange-200 bg-orange-50' :
              'border-yellow-200 bg-yellow-50'
            }`}>
              <Clock className={`h-4 w-4 ${
                isCritical ? 'text-red-600' :
                isUrgent ? 'text-orange-600' :
                'text-yellow-600'
              }`} />
              <AlertDescription className={`text-sm ${
                isCritical ? 'text-red-800' :
                isUrgent ? 'text-orange-800' :
                'text-yellow-800'
              }`}>
                Your session will expire in{' '}
                <Badge variant="outline" className={`font-mono ${
                  isCritical ? 'border-red-300 text-red-700' :
                  isUrgent ? 'border-orange-300 text-orange-700' :
                  'border-yellow-300 text-yellow-700'
                }`}>
                  {formatTimeLeft(timeLeft)}
                </Badge>
              </AlertDescription>
            </Alert>

            <div className="text-xs text-muted-foreground">
              {isCritical ? (
                'Your session is about to expire. Extend now to avoid being logged out.'
              ) : isUrgent ? (
                'Please extend your session to continue working without interruption.'
              ) : (
                'Consider extending your session to avoid losing your work.'
              )}
            </div>

            <div className="flex gap-2">
              <Button
                onClick={handleExtendSession}
                disabled={isExtending}
                size="sm"
                className={`flex-1 ${
                  isCritical ? 'bg-red-600 hover:bg-red-700' :
                  isUrgent ? 'bg-orange-600 hover:bg-orange-700' :
                  'bg-yellow-600 hover:bg-yellow-700'
                }`}
              >
                {isExtending ? (
                  <>
                    <RefreshCw className="h-3 w-3 mr-1 animate-spin" />
                    Extending...
                  </>
                ) : (
                  <>
                    <Clock className="h-3 w-3 mr-1" />
                    Extend Session
                  </>
                )}
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={dismissWarning}
                className="px-3"
              >
                Dismiss
              </Button>
            </div>

            {autoExtend && !hasAutoExtended && (
              <div className="text-xs text-muted-foreground text-center">
                Session will auto-extend when 1 minute remains
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

/**
 * Compact session timeout warning for headers/navigation
 */
export function SessionTimeoutBadge({
  className = '',
  warningThreshold = 5 * 60 * 1000
}: {
  className?: string;
  warningThreshold?: number;
}) {
  const { showWarning, timeLeft } = useSessionTimeoutWarning(warningThreshold);
  const [isExtending, setIsExtending] = useState(false);

  const handleExtend = async () => {
    setIsExtending(true);
    try {
      await refreshSession();
    } catch (error) {
      console.error('Failed to extend session:', error);
    } finally {
      setIsExtending(false);
    }
  };

  if (!showWarning || !timeLeft) {
    return null;
  }

  const formatTimeLeft = (milliseconds: number): string => {
    const totalSeconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const isUrgent = timeLeft <= 60000;

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      <Badge
        variant={isUrgent ? "destructive" : "secondary"}
        className="font-mono"
      >
        <Clock className="h-3 w-3 mr-1" />
        {formatTimeLeft(timeLeft)}
      </Badge>

      <Button
        variant="outline"
        size="sm"
        onClick={handleExtend}
        disabled={isExtending}
        className="h-6 px-2 text-xs"
      >
        {isExtending ? (
          <RefreshCw className="h-3 w-3 animate-spin" />
        ) : (
          'Extend'
        )}
      </Button>
    </div>
  );
}

/**
 * Global session timeout warning provider
 * Add this to your app layout to show warnings throughout the app
 */
export function SessionTimeoutProvider({
  children,
  warningThreshold = 5 * 60 * 1000,
  autoExtend = false
}: {
  children: React.ReactNode;
  warningThreshold?: number;
  autoExtend?: boolean;
}) {
  return (
    <>
      {children}
      <SessionTimeoutWarning
        warningThreshold={warningThreshold}
        autoExtend={autoExtend}
      />
    </>
  );
}
