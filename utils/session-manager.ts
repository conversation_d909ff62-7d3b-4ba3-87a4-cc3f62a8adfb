/**
 * Enhanced Client-Side Session Management
 *
 * Provides robust session validation and management for client-side components.
 * Implements caching, timeout handling, and security monitoring.
 */

import { createClient } from '@/utils/supabase/client';
import { applyDebugSessionState } from './session-debug';

export interface SessionInfo {
  isValid: boolean;
  isExpired: boolean;
  isExpiringSoon: boolean;
  expiresAt: number | null;
  timeUntilExpiry: number | null;
  user: any | null;
  error?: string;
  lastValidated: number;
  securityLevel: 'secure' | 'warning' | 'critical';
}

export interface SessionSecurityStatus {
  level: 'secure' | 'warning' | 'critical';
  message: string;
  icon: '✅' | '⚠️' | '🚨';
  details: string[];
  requiresAction: boolean;
}

// Session cache for performance optimization
let sessionCache: SessionInfo | null = null;
let cacheTimestamp = 0;
const CACHE_DURATION = 30 * 1000; // 30 seconds
const WARNING_THRESHOLD = 5 * 60 * 1000; // 5 minutes in milliseconds

/**
 * Calculate session timing information
 */
function calculateSessionTiming(expiresAt: number | null): {
  isExpiringSoon: boolean;
  timeUntilExpiry: number | null;
} {
  if (!expiresAt) {
    return { isExpiringSoon: false, timeUntilExpiry: null };
  }

  const now = Date.now();
  const expiryTime = expiresAt * 1000; // Convert to milliseconds
  const timeUntilExpiry = expiryTime - now;
  const isExpiringSoon = timeUntilExpiry <= WARNING_THRESHOLD && timeUntilExpiry > 0;

  return { isExpiringSoon, timeUntilExpiry };
}

/**
 * Determine security level based on session state
 */
function determineSecurityLevel(sessionInfo: Partial<SessionInfo>): 'secure' | 'warning' | 'critical' {
  if (!sessionInfo.isValid || sessionInfo.isExpired) {
    return 'critical';
  }

  if (sessionInfo.isExpiringSoon) {
    return 'warning';
  }

  return 'secure';
}

/**
 * Create a complete SessionInfo object with all required properties
 */
function createSessionInfo(
  isValid: boolean,
  isExpired: boolean,
  expiresAt: number | null,
  user: any | null,
  error?: string
): SessionInfo {
  const timing = calculateSessionTiming(expiresAt);
  const lastValidated = Date.now();

  const sessionInfo: Partial<SessionInfo> = {
    isValid,
    isExpired,
    expiresAt,
    user,
    error,
    lastValidated,
    ...timing
  };

  const securityLevel = determineSecurityLevel(sessionInfo);

  return {
    ...sessionInfo,
    securityLevel
  } as SessionInfo;
}

/**
 * Enhanced session validation with caching and performance optimization
 */
export async function validateSessionClient(useCache: boolean = true): Promise<SessionInfo> {
  try {
    // Check cache first if enabled
    if (useCache && sessionCache && (Date.now() - cacheTimestamp) < CACHE_DURATION) {
      return sessionCache;
    }

    const supabase = createClient();

    // Use getUser() for authentication validation
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError) {
      const result = createSessionInfo(false, false, null, null, userError.message);
      if (useCache) {
        sessionCache = result;
        cacheTimestamp = Date.now();
      }
      return result;
    }

    if (!user) {
      const result = createSessionInfo(false, false, null, null, 'No authenticated user');
      if (useCache) {
        sessionCache = result;
        cacheTimestamp = Date.now();
      }
      return result;
    }

    // Get session for expiration check
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError) {
      const result = createSessionInfo(false, false, null, user, sessionError.message);
      if (useCache) {
        sessionCache = result;
        cacheTimestamp = Date.now();
      }
      return result;
    }

    if (!session) {
      const result = createSessionInfo(false, false, null, user, 'No valid session');
      if (useCache) {
        sessionCache = result;
        cacheTimestamp = Date.now();
      }
      return result;
    }

    // Check if session is expired
    const now = Date.now() / 1000;
    const isExpired = session.expires_at ? session.expires_at < now : false;

    if (isExpired) {
      // Attempt to sign out the expired session
      try {
        await supabase.auth.signOut();
      } catch (signOutError) {
        console.error('Error signing out expired session:', signOutError);
      }

      const result = createSessionInfo(false, true, session.expires_at || null, user, 'Session expired');
      if (useCache) {
        sessionCache = result;
        cacheTimestamp = Date.now();
      }
      return result;
    }

    const result = createSessionInfo(true, false, session.expires_at || null, user);
    if (useCache) {
      sessionCache = result;
      cacheTimestamp = Date.now();
    }

    // Apply debug state modifications in development
    return applyDebugSessionState(result);
  } catch (error) {
    console.error('Error validating client session:', error);
    const result = createSessionInfo(
      false,
      false,
      null,
      null,
      error instanceof Error ? error.message : 'Unknown error'
    );
    return result;
  }
}

/**
 * Force session refresh and clear cache
 * Useful when session might be stale
 */
export async function refreshSession(): Promise<SessionInfo> {
  try {
    const supabase = createClient();

    // Clear cache before refresh
    sessionCache = null;
    cacheTimestamp = 0;

    // Force refresh the session
    const { data: { session }, error } = await supabase.auth.refreshSession();

    if (error) {
      return createSessionInfo(false, false, null, null, error.message);
    }

    if (!session) {
      return createSessionInfo(false, false, null, null, 'Failed to refresh session');
    }

    const result = createSessionInfo(true, false, session.expires_at || null, session.user);

    // Update cache with refreshed session
    sessionCache = result;
    cacheTimestamp = Date.now();

    return result;
  } catch (error) {
    console.error('Error refreshing session:', error);
    return createSessionInfo(
      false,
      false,
      null,
      null,
      error instanceof Error ? error.message : 'Unknown error'
    );
  }
}

/**
 * Clear session cache
 * Forces next validation to check with server
 */
export function clearSessionCache(): void {
  sessionCache = null;
  cacheTimestamp = 0;
}

/**
 * Get session security status for UI display
 */
export function getSessionSecurityStatus(sessionInfo: SessionInfo): SessionSecurityStatus {
  const details: string[] = [];

  if (sessionInfo.isValid) {
    details.push('Session is valid and authenticated');

    if (sessionInfo.timeUntilExpiry) {
      const minutes = Math.floor(sessionInfo.timeUntilExpiry / (1000 * 60));
      const hours = Math.floor(minutes / 60);

      if (hours > 0) {
        details.push(`Session expires in ${hours}h ${minutes % 60}m`);
      } else {
        details.push(`Session expires in ${minutes}m`);
      }
    }
  } else {
    details.push(sessionInfo.error || 'Session validation failed');
  }

  switch (sessionInfo.securityLevel) {
    case 'secure':
      return {
        level: 'secure',
        message: 'Session Secure',
        icon: '✅',
        details,
        requiresAction: false
      };

    case 'warning':
      return {
        level: 'warning',
        message: 'Session Expiring Soon',
        icon: '⚠️',
        details: [...details, 'Consider refreshing your session'],
        requiresAction: true
      };

    case 'critical':
      return {
        level: 'critical',
        message: sessionInfo.isExpired ? 'Session Expired' : 'Authentication Required',
        icon: '🚨',
        details: [...details, 'Please sign in again'],
        requiresAction: true
      };

    default:
      return {
        level: 'critical',
        message: 'Unknown Security State',
        icon: '🚨',
        details: ['Unable to determine session security status'],
        requiresAction: true
      };
  }
}

/**
 * Check if session needs refresh (expiring within 10 minutes)
 */
export function shouldRefreshSession(sessionInfo: SessionInfo): boolean {
  if (!sessionInfo.isValid || !sessionInfo.timeUntilExpiry) {
    return false;
  }

  const tenMinutes = 10 * 60 * 1000; // 10 minutes in milliseconds
  return sessionInfo.timeUntilExpiry <= tenMinutes && sessionInfo.timeUntilExpiry > 0;
}

/**
 * Get session timeout in seconds
 * Returns the time until session expires
 */
export function getSessionTimeout(expiresAt: number | null): number {
  if (!expiresAt) {
    return 0;
  }

  const now = Date.now() / 1000;
  const timeout = expiresAt - now;

  return Math.max(0, timeout);
}

/**
 * Check if session will expire soon (within 5 minutes)
 */
export function isSessionExpiringSoon(expiresAt: number | null): boolean {
  if (!expiresAt) {
    return false;
  }

  const timeout = getSessionTimeout(expiresAt);
  const fiveMinutes = 5 * 60; // 5 minutes in seconds

  return timeout <= fiveMinutes && timeout > 0;
}
